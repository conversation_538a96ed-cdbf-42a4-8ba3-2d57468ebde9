/**
 * Test script to verify webhook logging during actual webhook processing
 * 
 * This script simulates webhook calls to test the logging in the actual webhook handlers
 */

import { Hono } from "hono";
import { handleWebhookEvent } from "./src/webhook/handler";
import { handleAPContactWebhook, handleAPAppointmentWebhook } from "./src/webhook/apHandler";

// Create a test Hono app
const testApp = new Hono();

// Mock webhook payloads
const ccWebhookPayload = {
  event: "EntityWasCreated",
  model: "Patient",
  id: 12345,
  payload: {
    id: 12345,
    firstName: "<PERSON>",
    lastName: "Doe",
    email: "<EMAIL>",
    phoneMobile: "+**********",
    dateOfBirth: "1990-01-01",
    createdAt: "2024-01-01T10:00:00Z",
    updatedAt: "2024-01-01T10:00:00Z"
  }
};

const apContactWebhookPayload = {
  type: "ContactCreate",
  locationId: "test_location_123",
  data: {
    contact: {
      id: "ap_contact_123",
      firstName: "<PERSON>",
      lastName: "<PERSON>",
      email: "<EMAIL>",
      phone: "+**********",
      tags: ["cc_api"],
      customFields: [
        {
          id: "field_123",
          value: "Test Value"
        }
      ],
      dateAdded: "2024-01-01T10:00:00Z"
    }
  }
};

const apAppointmentWebhookPayload = {
  type: "CalendarEventCreate",
  locationId: "test_location_123",
  data: {
    calendar_event: {
      id: "ap_appointment_123",
      contactId: "ap_contact_123",
      title: "Test Appointment",
      startTime: "2024-01-01T10:00:00Z",
      endTime: "2024-01-01T11:00:00Z",
      status: "confirmed"
    }
  }
};

// Mock Request and Context objects
function createMockContext(body: any) {
  return {
    req: {
      json: async () => body,
      header: (name: string) => {
        if (name === "x-request-id") return "test_request_123";
        return undefined;
      }
    },
    json: (data: any, status?: number) => {
      console.log(`Response (${status || 200}):`, JSON.stringify(data, null, 2));
      return { data, status: status || 200 };
    },
    text: (text: string, status?: number) => {
      console.log(`Text Response (${status || 200}):`, text);
      return { text, status: status || 200 };
    }
  } as any;
}

async function testCCWebhook() {
  console.log("\n🔄 Testing CC Webhook Handler");
  console.log("==============================");
  
  const context = createMockContext(ccWebhookPayload);
  
  try {
    const result = await handleWebhookEvent(context);
    console.log("CC Webhook completed successfully");
  } catch (error) {
    console.log("CC Webhook failed (expected due to test data):", error.message);
  }
}

async function testAPContactWebhook() {
  console.log("\n👤 Testing AP Contact Webhook Handler");
  console.log("=====================================");
  
  const context = createMockContext(apContactWebhookPayload);
  
  try {
    const result = await handleAPContactWebhook(context);
    console.log("AP Contact Webhook completed successfully");
  } catch (error) {
    console.log("AP Contact Webhook failed (expected due to test data):", error.message);
  }
}

async function testAPAppointmentWebhook() {
  console.log("\n📅 Testing AP Appointment Webhook Handler");
  console.log("=========================================");
  
  const context = createMockContext(apAppointmentWebhookPayload);
  
  try {
    const result = await handleAPAppointmentWebhook(context);
    console.log("AP Appointment Webhook completed successfully");
  } catch (error) {
    console.log("AP Appointment Webhook failed (expected due to test data):", error.message);
  }
}

async function main() {
  console.log("🧪 Testing Webhook Logging During Processing");
  console.log("============================================");
  console.log("This test will trigger actual webhook handlers to verify API logging appears");
  console.log("Look for colorful API logs with 🚀, ✅, ❌, and 💥 emojis\n");
  
  await testCCWebhook();
  await testAPContactWebhook();
  await testAPAppointmentWebhook();
  
  console.log("\n✅ Webhook logging test completed");
  console.log("If you saw API logs with emojis and correlation IDs, the logging is working correctly!");
}

// Run the test
main().catch(console.error);
