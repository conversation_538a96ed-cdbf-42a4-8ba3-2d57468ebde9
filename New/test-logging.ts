/**
 * Test script to verify API logging system is working correctly
 * 
 * This script tests:
 * 1. Basic logger functionality
 * 2. API logging functions
 * 3. Actual API calls with logging
 * 4. Console output verification
 */

import { logger } from "./src/utils/logger";
import { 
  createApiRequestContext, 
  logApiRequest, 
  logApiResponse, 
  logApiError 
} from "./src/utils/apiLogger";
import { ccClient, apClient } from "./src/api";
import type { RequestOptions } from "./src/api/request";

async function testBasicLogger() {
  console.log("\n=== Testing Basic Logger ===");
  
  logger.info("Test info message", { test: "data", number: 123 });
  logger.debug("Test debug message", { debug: true, array: [1, 2, 3] });
  logger.error("Test error message", { error: "test error" });
  logger.warn("Test warning message");
  logger.success("Test success message");
}

async function testApiLoggingFunctions() {
  console.log("\n=== Testing API Logging Functions ===");

  // Test GET request (no payload)
  const getOptions: RequestOptions = {
    url: "/patients/123",
    method: "GET",
    params: { include: "appointments" }
  };

  const mockHeaders = {
    "Authorization": "Bearer test_token_**********_abcdefghij",
    "Content-Type": "application/json"
  };

  const getContext = createApiRequestContext(
    "https://ccdemo.clinicore.eu/api/v1",
    getOptions,
    mockHeaders
  );

  console.log("Created GET API context:", getContext);
  logApiRequest(getContext, getOptions);
  logApiResponse(getContext, 200, true, 1024);

  // Test POST request with payload (should show debug log)
  const postOptions: RequestOptions = {
    url: "/contacts",
    method: "POST",
    data: {
      firstName: "John",
      lastName: "Doe",
      email: "<EMAIL>",
      phone: "+**********",
      customFields: [
        { id: "field_123", value: "Test Value" }
      ]
    }
  };

  const postContext = createApiRequestContext(
    "https://services.leadconnectorhq.com",
    postOptions,
    mockHeaders
  );

  console.log("\nTesting POST request with payload:");
  logApiRequest(postContext, postOptions);
  logApiResponse(postContext, 400, false, 512);

  // Test logApiError
  const error = new Error("Test API error");
  logApiError(postContext, error, 1, 3);
}

async function testActualApiCalls() {
  console.log("\n=== Testing Actual API Calls ===");
  
  try {
    console.log("Testing CC API call...");
    // This will likely fail due to auth, but should show logging
    await ccClient.patient.get(999999);
  } catch (error) {
    console.log("CC API call failed as expected:", error.message);
  }
  
  try {
    console.log("Testing AP API call...");
    // This will likely fail due to auth, but should show logging
    await apClient.contact.get("test_contact_id");
  } catch (error) {
    console.log("AP API call failed as expected:", error.message);
  }
}

async function testConsoleOutput() {
  console.log("\n=== Testing Console Output Methods ===");
  
  console.log("Standard console.log");
  console.info("Standard console.info");
  console.debug("Standard console.debug");
  console.warn("Standard console.warn");
  console.error("Standard console.error");
}

async function main() {
  console.log("🧪 Starting API Logging System Test");
  console.log("=====================================");
  
  await testBasicLogger();
  await testApiLoggingFunctions();
  await testConsoleOutput();
  await testActualApiCalls();
  
  console.log("\n✅ Test completed - check console output above");
  console.log("If you don't see colorful logs with timestamps, there's an issue with the logging system");
}

// Run the test
main().catch(console.error);
